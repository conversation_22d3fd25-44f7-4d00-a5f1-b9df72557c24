/**
 * Frontend logging utility
 * Provides centralized logging with environment-based configuration
 */

// Get configuration from environment
const LOG_LEVEL = import.meta.env.VITE_LOG_LEVEL || "info";
const DEBUG_ENABLED = import.meta.env.VITE_DEBUG === "true";
const LOG_TO_CONSOLE = import.meta.env.VITE_LOG_TO_CONSOLE !== "false";
const LOG_TO_SERVER = import.meta.env.VITE_LOG_TO_SERVER === "true";
const LOG_API_REQUESTS = import.meta.env.VITE_LOG_API_REQUESTS !== "false";
const LOG_PERFORMANCE = import.meta.env.VITE_LOG_PERFORMANCE === "true";
const LOG_USER_ACTIONS = import.meta.env.VITE_LOG_USER_ACTIONS !== "false";

// Log levels in order of severity
const LOG_LEVELS = {
  debug: 0,
  info: 1,
  warning: 2,
  error: 3,
  critical: 4,
};

// Current log level threshold - if DEBUG is true, use debug level
const currentLogLevel = DEBUG_ENABLED
  ? LOG_LEVELS.debug
  : LOG_LEVELS[LOG_LEVEL.toLowerCase()] || LOG_LEVELS.info;

/**
 * Check if a log level should be output
 * @param {string} level - The log level to check
 * @returns {boolean} - Whether the log should be output
 */
function shouldLog(level) {
  return LOG_LEVELS[level] >= currentLogLevel;
}

/**
 * Format log message with timestamp and context
 * @param {string} level - Log level
 * @param {string} message - Log message
 * @param {Object} context - Additional context data
 * @returns {Object} - Formatted log entry
 */
function formatLogMessage(level, message, context = {}) {
  return {
    timestamp: new Date().toISOString(),
    level: level.toUpperCase(),
    message,
    ...context,
    // Add browser info for frontend logs
    userAgent: navigator.userAgent,
    url: window.location.href,
  };
}

/**
 * Send log to console with appropriate styling
 * @param {string} level - Log level
 * @param {Object} logEntry - Formatted log entry
 */
function outputToConsole(level, logEntry) {
  const styles = {
    debug: "color: #6B7280; font-weight: normal;",
    info: "color: #3B82F6; font-weight: normal;",
    warning: "color: #F59E0B; font-weight: bold;",
    error: "color: #EF4444; font-weight: bold;",
    critical: "color: #DC2626; font-weight: bold; background: #FEE2E2;",
  };

  const style = styles[level] || styles.info;
  const prefix = `%c[${logEntry.level}] ${logEntry.timestamp}`;

  // Create a clean copy of logEntry for console output
  const cleanLogEntry = { ...logEntry };

  // Handle cases where message might be an object
  const message =
    typeof logEntry.message === "string"
      ? logEntry.message
      : JSON.stringify(logEntry.message);

  if (level === "error" || level === "critical") {
    // eslint-disable-next-line no-console
    console.error(prefix, style, message, cleanLogEntry);
  } else if (level === "warning") {
    // eslint-disable-next-line no-console
    console.warn(prefix, style, message, cleanLogEntry);
  } else {
    // eslint-disable-next-line no-console
    console.log(prefix, style, message, cleanLogEntry);
  }
}

/**
 * Core logging function
 * @param {string} level - Log level
 * @param {string} message - Log message
 * @param {Object} context - Additional context data
 */
function log(level, message, context = {}) {
  if (!shouldLog(level)) {
    return;
  }

  const logEntry = formatLogMessage(level, message, context);

  // Output to console if enabled
  if (LOG_TO_CONSOLE) {
    outputToConsole(level, logEntry);
  }

  // Send to server if enabled and in production
  if (
    LOG_TO_SERVER &&
    import.meta.env.PROD &&
    (level === "error" || level === "critical")
  ) {
    sendToLoggingService(logEntry);
  }
}

/**
 * Send log entry to server logging service
 * @param {Object} logEntry - Formatted log entry
 */
async function sendToLoggingService(logEntry) {
  try {
    // Only send in production to avoid development noise
    if (import.meta.env.DEV) return;

    // Server logging not implemented yet
    console.warn("Server logging not implemented", logEntry);
  } catch (error) {
    log("warning", "Failed to send log to server", { error });
  }
}

// Exported logging functions
export const debug = (message, context = {}) => log("debug", message, context);
export const info = (message, context = {}) => log("info", message, context);
export const warning = (message, context = {}) =>
  log("warning", message, context);
export const error = (message, context = {}) => log("error", message, context);
export const critical = (message, context = {}) =>
  log("critical", message, context);

// Specialized logging functions
export const logApiRequest = (method, endpoint, data = null, context = {}) => {
  if (!LOG_API_REQUESTS) return;

  debug(`API Request: ${method} ${endpoint}`, {
    method,
    endpoint,
    data,
    type: "api_request",
    ...context,
  });
};

export const logApiResponse = (
  method,
  endpoint,
  status,
  data = null,
  context = {}
) => {
  if (!LOG_API_REQUESTS) return;

  const level = status >= 400 ? "error" : "debug";
  log(level, `API Response: ${method} ${endpoint} - ${status}`, {
    method,
    endpoint,
    status,
    data,
    type: "api_response",
    ...context,
  });
};

export const logApiError = (method, endpoint, error, context = {}) => {
  if (!LOG_API_REQUESTS) return;

  // Better error serialization for logging
  let errorMessage = "Unknown error";
  let errorDetails = {};

  if (error) {
    if (typeof error === "string") {
      errorMessage = error;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail;
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (typeof error === "object") {
      errorMessage = JSON.stringify(error);
    }

    // Extract additional error details
    if (error.response) {
      errorDetails = {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        headers: error.response.headers,
      };
    }

    if (error.stack) {
      errorDetails.stack = error.stack;
    }

    if (error.code) {
      errorDetails.code = error.code;
    }
  }

  log("error", `API Error: ${method} ${endpoint}: ${errorMessage}`, {
    method,
    endpoint,
    error: errorMessage,
    errorDetails,
    type: "api_error",
    ...context,
  });
};

export const logUserAction = (action, details = {}, context = {}) => {
  if (!LOG_USER_ACTIONS) return;

  info(`User Action: ${action}`, {
    action,
    details,
    type: "user_action",
    ...context,
  });
};

export const logNavigation = (from, to, context = {}) => {
  if (!LOG_USER_ACTIONS) return;

  debug(`Navigation: ${from} -> ${to}`, {
    from,
    to,
    type: "navigation",
    ...context,
  });
};

export const logPerformance = (operation, duration, context = {}) => {
  if (!LOG_PERFORMANCE) return;

  debug(`Performance: ${operation} took ${duration}ms`, {
    operation,
    duration,
    type: "performance",
    ...context,
  });
};


